package Protocol

type RuleData struct {
	Name string
	Type string
	Rule string
}

var RuleDatas = []RuleData{
	{"bootstrap", "body", "(bootstrap)"},
	{"Nextcloud", "body", "(Nextcloud)"},
	{"兰空图床", "body", "(兰空图床|Lsky Pro)"},
	{"Exchange", "body", "(Outlook)|/owa/"},
	{"APACHE-ActiveMQ", "body", "(Apache ActiveMQ)"},
	{"Jetty", "body", "(Powered by <PERSON>y)"},
	{"华夏ERP", "body", "(jshERP-boo)"},
	{"Lightdash", "body", "(Lightdash)"},
	{"Apache-storm", "body", "(Storm UI)"},
	{"HiveServer", "body", "(HiveServer)"},
	{"JupyterLab", "body", "(JupyterLab)"},
	{"华测监测预警系统", "body", "(华测监测预警系统)"},
	{"时空智友企业信息管理", "body", "(时空智友企业信息管理)"},
	{"D-Link-Route", "server", "HTTPD_ac 1.0"},
	{"Kibana", "body", "(kibanaLegacy|.kbnLoader|<title>Kibana</title>)"},
	{"Node-Exporter", "body", "(Node Exporter)"},
	{"Prometheus", "body", "(Prometheus)"},
	{"Ambari", "body", "(Ambari uses and their respective authors)"},
	{"docker-registry", "body", "(docker-registry-frontend)"},
	{"ThinkPHP", "body", "(ApiAdmin开发维护团队|ThinkPHP)"},
	{"亿邮电子邮件系统", "body", "(亿邮电子邮件系统)"},
	{"华途应用安全网关", "body", "(<title>应用安全网关 - Powered By asg</title>)"},
	{"Casbin", "body", "(<title>Casdoor</title>)"},
	{"Crawlab", "body", "(<title>Crawlab</title>|<title>Crawlab Pro</title>)"},
	{"Jupyter", "body", "(<title>Jupyter Notebook</title>)"},
	{"企望制造ERP系统", "body", "(企望制造ERP系统)"},
	{"Node-RED", "body", "(<title>Node-RED</title>)"},
	{"用友NC", "body", "(url=nccloud|YONYOU NC)"},
	{"小米路由器", "headers", "(Micgi-Preload)"},
	{"小米路由器", "body", "(<title>小米路由器</title>)"},
	{"Everything", "body", "Everything"},
	{"大华城市安防监控系统平台", "body", "(User: Selene)"},
	{"Jenkins", "headers", "Jenkins"},
	{"Kafka-Manager", "headers", "Kafka-Manager"},
	{"vhost-Panel", "body", "This is the default server vhost"},
	{"大华智慧园区综合管理平台", "body", "(/WPMS/asset/lib/gridster/|URL='/WPMS')"},
	{"Baidu-ECharts", "body", "(echarts\\.js|echarts\\.min\\.js)"},
	{"Kafka-Manager[未授权访问]", "body", "(<title>Kafka Manager</title>)"},
	{"Jumpserver堡垒机", "body", "(Jumpserver|全球首款完全开源的堡垒机|Jumpserver.css)"},
	{"天融信-入侵检测系统TopSentry", "body", "(<title>天融信入侵检测系统TopSentry</title>|TopSentry)"},
	{"天融信-入侵防御系统TopIDP", "body", "(<title>天融信入侵防御系统TopIDP</title>|TopIDP)"},
	{"天融信-TOPSEC", "body", "(/site/js/xblib.js)"},
	{"绿盟科技认证系统", "body", "用户认证 - NSFOCUS NF|绿盟科技认证系统"},
	{"HIKVISION-视频监控系统", "body", "(/doc/page/login.asp)"},
	{"Coremail邮件系统", "body", "(Coremail邮件系统)"},
	{"阿里云OSS", "body", "(<Code>AccessDenied</Code>)"},
	{"frp", "body", "(Faithfully yours, frp)"},
	{"Spark", "body", "(serverSparkVersion)"},
	{"Apache-Spark", "body", "(Spark Worker at)"},
	{"数据库|CouchDB[未授权访问]", "body", "(couchdb.*?uuid)"},
	{"Hadoop-Administration", "body", "(DataNode Information|Hadoop Administration)"},
	{"go-pprof", "body", "(Node Exporter)"},
	{"Django", "body", "(DisallowedHost|django.template|django/core/handlers/exception.py)"},
	{"Grafana", "body", "(Grafana)"},
	{"深信服-上网行为管理系统", "body", "(Internet Authentication System|<title>上网认证系统</title>)"},
	{"深信服-防火墙", "body", "(function\\(str, key\\)|LogInOut.php)"},
	{"任子行-防火墙", "body", "(任子行下一代防火墙)"},
	{"NPS内网穿透", "body", "(ehang.io/nps|<title>nps error</title>)"},
	{"华为路由器", "body", "(/css/cat_public.css.cgz|/api/system/routerstatus)"},
	{"Shiro", "cookie", "(rememberMe)"},
	{"Struts2", "body", "(Struts2 Showcase|org.apache.struts2|Struts Problem Report|struts.devMode|struts-tags|There is no Action mapped for namespace)"},
	{"qBittorrent-Web-UI", "body", "(qBittorrent Web UI)"},
	{"织梦内容管理系统", "body", "(织梦内容管理系统)"},
	{"宝塔", "body", "(app.bt.cn/static/app.png|安全入口校验失败|<title>入口校验失败</title>|href=\"http://www.bt.cn/bbs|恭喜, 站点创建成功！)"},
	{"启明防火墙", "body", "(/cgi-bin/webui?op=get_product_model)"},
	{"数据库|ElasticSearch[未授权访问]", "body", `(?s)"name"\s*:\s*"[^"]*".*?"cluster_name"\s*:\s*"[^"]*".*?"cluster_uuid"\s*:\s*"[^"]*".*?"number"\s*:\s*"[^"]*"`},
	{"数据库|ElasticSearch", "body", "security_exception"},
	{"AList", "body", "(由 AList 驱动|alist_pic.js)"},
	{"数据库「MongoDB」", "body", `(MongoDB)`},
	{"ZABBIX-监控系统", "body", "(Zabbix SIA|<title>omni: Zabbix</title>|images/general/zabbix.ico|Zabbix SIA|zabbix-server: Zabbix)"},
	{"phpinfo", "body", "(<title>(.*phpinfo.*)</title>)"},
	{"Tomcat", "body", "(<title>(.*Tomcat.*)</title>|Manager App|Apache Tomcat)"},
	{"天融信VPN", "body", "(/portal_default/index.html)"},
	{"SonarQube-代码管理", "body", "(SonarQube)"},
	{"TamronOS-IPTV", "body", "(TamronOS IPTV系统|IPTV/VOD系统)"},
	{"安恒信息-明御运维审计与风险控制系统", "body", "(<title>(.*明御运维审计.*)</title>)"},
	{"知道创宇-WEBSOC", "body", "(<title>\\s*登录\\s*-\\s*WebSOC知道网站立体监控系统\\s*-\\s*知道创宇\\s*</title>)"},
	{"联软网络智能准入系统", "body", "(下载助手安装包|href=.*portal/resources/css/auth_login.css?)"},
	{"联软IT安全运维管理系统", "body", "(/manager/Resource/Js/ajax.js)"},
	{"phpPgAdmin", "body", "(<title>phpPgAdmin</title>)"},
	{"ThinkPHP", "headers", "(ThinkPHP)"},
	{"ActiveMQ", "headers", "(ActiveMQRealm)"},
	{"VisualSVN", "headers", "(VisualSVN Server)"},
	{"ThinkPHP", "cookie", "(think_var)"},
	{"vmware-ESX", "cookie", "(vmware_esx_host|vmware_client)"},
	{"vmware-ESX", "body", "(URL='/ui')"},
	{"RouterOS", "body", "(mikrotik_logo.png)"},
	{"jQuery", "body", "(jquery.*?js)"},
	{"MinIO", "body", "(MinIO Console|<title>MinIO Browser</title>)"},
	{"MinIO", "headers", "MinIO"},
	{"Spring", "body", "(Whitelabel Error Page|No message available|令牌不能为空|timestamp.*?404.*?Not Found|timestamp.*?500.*?Internal Server Error)"},
	{"H3C-Switch", "body", "(../images/Cnlink.jpg|Web user login)"},
	{"H3C-安全管理平台", "body", "(安全产品管理平台|<title>Web managerment Home</title>)"},
	{"360网站卫士", "body", "(webscan.360.cn/status/pai/hash|wzws-waf-cgi|zhuji.360.cn/guard/firewall/stopattack.html)"},
	{"绿盟防火墙", "body", "(NSFOCUS NF)"},
	{"Safedog", "body", "(404.safedog.cn/images/safedogsite/broswer_logo.jpg)"},
	{"阿里云", "body", "(errors.aliyun.com)"},
	{"Portainer(Docker管理)", "body", "(portainer.updatePassword|portainer.init.admin)"},
	{"Nexus", "body", "(Nexus Repository Manager)"},
	{"Harbor", "body", "(<title>Harbor</title>)"},
	{"禅道", "body", "(/theme/default/images/main/zt-logo.png)"},
	{"协众OA", "body", "(Powered by 协众OA)"},
	{"xxl-job", "body", "(分布式任务调度平台XXL-JOB)"},
	{"atmail-WebMail", "body", "(/index.php/mail/auth/processlogin|Powered by Atmail)"},
	{"weblogic", "body", "(/console/framework/skins/wlsconsole/images/login_WebLogic_branding.png|Welcome to Weblogic Application Server|<i>Hypertext Transfer Protocol -- HTTP/1.1</i>)"},
	{"致远OA", "body", "(/seeyon/common/|/seeyon/USER-DATA/IMAGES/LOGIN/login.gif)"},
	{"discuz", "body", "(content=\"Discuz! X\")"},
	{"Typecho", "body", "(Typecho</a>)"},
	{"金蝶EAS", "body", "(easSessionId)"},
	{"phpMyAdmin", "body", "(/themes/pmahomme/img/logo_right.png)"},
	{"H3C-AM8000", "body", "(AM8000)"},
	{"360企业版", "body", "(360EntWebAdminMD5Secret)"},
	{"H3C公司产品", "body", "(<EMAIL>)"},
	{"H3C ICG 1000", "body", "(ICG 1000系统管理)"},
	{"Citrix-Metaframe", "body", "(window.location=\"/Citrix/MetaFrame)"},
	{"H3C ER5100", "body", "(ER5100系统管理)"},
	{"阿里云CDN", "body", "(cdn.aliyuncs.com)"},
	{"CISCO_EPC3925", "body", "(Docsis_system)"},
	{"CISCO ASR", "body", "(CISCO ASR)"},
	{"H3C ER3200", "body", "(ER3200系统管理)"},
	{"万户OA", "body", "(/defaultroot/templates/template_system/common/css/|/defaultroot/scripts/|css/css_whir.css)"},
	{"Spark_Master", "body", "(Spark Master at)"},
	{"nginxWebUI", "body", "(nginxWebUI)"},
	{"华为_HUAWEI_SRG2220", "body", "(HUAWEI SRG2220)"},
	{"蓝凌OA", "body", "(/scripts/jquery.landray.common.js)"},
	{"深信服ssl-vpn", "body", "(login_psw.csp)"},
	{"华为 NetOpen", "body", "(/netopen/theme/css/inFrame.css)"},
	{"Citrix-Web-PN-Server", "body", "(Citrix Web PN Server)"},
	{"juniper_vpn", "body", "(welcome.cgi?p=logo|/images/logo_juniper_reversed.gif)"},
	{"H3C ER8300", "body", "(ER8300系统管理)"},
	{"Citrix-Access-Gateway", "body", "(Citrix Access Gateway)"},
	{"华为 MCU", "body", "(McuR5-min.js)"},
	{"TP-LINK Wireless WDR3600", "body", "(TP-LINK Wireless WDR3600)"},
	{"泛微OA", "body", "(/spa/portal/public/index.js)"},
	{"华为_HUAWEI_ASG2050", "body", "(HUAWEI ASG2050)"},
	{"360网站卫士", "body", "(360wzb)"},
	{"Citrix-XenServer", "body", "(Citrix Systems, Inc. XenServer)"},
	{"H3C ER2100V2", "body", "(ER2100V2系统管理)"},
	{"360站长平台", "body", "(360-site-verification)"},
	{"H3C ER3108GW", "body", "(ER3108GW系统管理)"},
	{"H3C ER3260G2", "body", "(ER3260G2系统管理)"},
	{"H3C ICG1000", "body", "(ICG1000系统管理)"},
	{"CISCO-CX20", "body", "(CISCO-CX20)"},
	{"H3C ER5200", "body", "(ER5200系统管理)"},
	{"linksys-vpn-bragap14-parintins", "body", "(linksys-vpn-bragap14-parintins)"},
	{"360网站卫士常用前端公共库", "body", "(libs.useso.com)"},
	{"H3C ER3100", "body", "(ER3100系统管理)"},
	{"360webfacil_360WebManager", "body", "(publico/template/)"},
	{"Citrix_Netscaler", "body", "(ns_af)"},
	{"H3C ER6300G2", "body", "(ER6300G2系统管理)"},
	{"H3C ER3260", "body", "(ER3260系统管理)"},
	{"华为_HUAWEI_SRG3250", "body", "(HUAWEI SRG3250)"},
	{"exchange", "body", "(/owa/auth.owa|Exchange Admin Center)"},
	{"H3C ER3108G", "body", "(ER3108G系统管理)"},
	{"Citrix-ConfProxy", "body", "(confproxy)"},
	{"360网站安全检测", "body", "(webscan.360.cn/status/pai/hash)"},
	{"H3C ER5200G2", "body", "(ER5200G2系统管理)"},
	{"华为（HUAWEI）安全设备", "body", "(sweb-lib/resource/)"},
	{"华为（HUAWEI）USG", "body", "(UI_component/commonDefine/UI_regex_define.js)"},
	{"H3C ER6300", "body", "(ER6300系统管理)"},
	{"华为_HUAWEI_ASG2100", "body", "(HUAWEI ASG2100)"},
	{"TP-Link 3600 DD-WRT", "body", "(TP-Link 3600 DD-WRT)"},
	{"NETGEAR WNDR3600", "body", "(NETGEAR WNDR3600)"},
	{"H3C ER2100", "body", "(ER2100系统管理)"},
	{"jira", "body", "(jira.webresources)"},
	{"金和协同管理平台", "body", "(金和协同管理平台)"},
	{"Citrix-NetScaler", "body", "(NS-CACHE)"},
	{"通达OA", "body", "(/static/images/tongda.ico|http://www.tongda2000.com|通达OA移动版|Office Anywhere)"},
	{"华为（HUAWEI）Secoway设备", "body", "(Secoway)"},
	{"华为_HUAWEI_SRG1220", "body", "(HUAWEI SRG1220)"},
	{"H3C ER2100n", "body", "(ER2100n系统管理)"},
	{"H3C ER8300G2", "body", "(ER8300G2系统管理)"},
	{"金蝶政务GSiS", "body", "(/kdgs/script/kdgs.js)"},
	{"Jboss", "body", "(Welcome to JBoss|jboss.css)"},
	{"泛微E-mobile", "body", "(Weaver E-mobile|weaver,e-mobile)"},
	{"齐治堡垒机", "body", "(logo-icon-ico72.png|resources/themes/images/logo-login.png)"},
	{"ThinkPHP", "body", "(/Public/static/js/)"},
	{"DWR", "body", "(dwr/engine.js)"},
	{"swagger_ui", "body", "(swagger-ui/css|\"swagger\":|swagger-ui.min.js)"},
	{"大汉版通发布系统", "body", "(大汉版通发布系统|大汉网络)"},
	{"YAPI", "body", "(YApi-高效、易用、功能强大的可视化接口管理平台)"},
	{"Apache-Druid", "body", "(druid.index|DruidDrivers|DruidVersion|Druid Stat Index|Apache Druid)"},
	{"Zeppelin", "body", "(Welcome to Zeppelin!)"},
	{"红帆OA", "body", "(iOffice)"},
	{"VMware vSphere", "body", "(VMware vSphere)"},
	{"打印机", "body", "(更换硒鼓|media/canon.gif|<title>*Brother*</title>|耗材商店|碳粉盒|耗材量严重不足时)"},
	{"finereport", "body", "(isSupportForgetPwd|FineReport,Web Reporting Tool)"},
	{"蓝凌OA", "body", "(蓝凌软件|StylePath:\"/resource/style/default/\"|/resource/customization|sys/ui/extend/theme/default/style/profile.css|sys/ui/extend/theme/default/style/icon.css)"},
	{"GitLab", "body", "(href=\"https://about.gitlab.com/)"},
	{"JQuery-1.7.2", "body", "(/webui/js/jquerylib/jquery-1.7.2.min.js)"},
	{"Hadoop Applications", "body", "(/cluster/app/application)"},
	{"海昌OA", "body", "(/loginmain4/js/jquery.min.js)"},
	{"帆软报表", "body", "(WebReport/login.html|ReportServer)"},
	{"久其财务报表", "body", "(netrep/login.jsp|/netrep/intf)"},
	{"若依管理系统", "body", "(ruoyi/login.js|ruoyi/js/ry-ui.js|<title>若依管理系统</title>)"},
	{"启莱OA", "body", "(js/jQselect.js|js/jquery-1.4.2.min.js)"},
	{"智慧校园管理系统", "body", "(DC_Login/QYSignUp)"},
	{"浪潮 ClusterEngineV4.0", "body", "(0;url=module/login/login.html)"},
	{"会捷通云视讯平台", "body", "(him/api/rest/v1.0/node/role|him.app)"},
	{"源码泄露账号密码 F12查看", "body", "(get_dkey_passwd)"},
	{"Smartbi Insight", "body", "(smartbi.gcf.gcfutil)"},
	{"汉王人脸考勤管理系统", "body", "(汉王人脸考勤管理系统|/Content/image/hanvan.png|/Content/image/hvicon.ico)"},
	{"亿赛通-电子文档安全管理系统", "body", "(电子文档安全管理系统|/CDGServer3/index.jsp|/CDGServer3/SysConfig.jsp|/CDGServer3/help/getEditionInfo.jsp)"},
	{"天融信 TopApp-LB 负载均衡系统", "body", "(TopApp-LB 负载均衡系统)"},
	{"中新金盾信息安全管理系统", "body", "(中新金盾信息安全管理系统|中新网络信息安全股份有限公司)"},
	{"好视通", "body", "(深圳银澎云计算有限公司|itunes.apple.com/us/app/id549407870|hao-shi-tong-yun-hui-yi-yuan)"},
	{"蓝海卓越计费管理系统", "body", "(蓝海卓越计费管理系统|星锐蓝海网络科技有限公司)"},
	{"和信创天云桌面系统", "body", "(和信下一代云桌面VENGD|/vesystem/index.php)"},
	{"金山", "body", "(北京猎鹰安全科技有限公司|金山终端安全系统V9.0Web控制台|北京金山安全管理系统技术有限公司|金山V8)"},
	{"MetInfo-米拓建站", "body", "(MetInfo|/skin/style/metinfo.css|/skin/style/metinfo-v2.css)"},
	{"IBM-Lotus-Domino", "body", "(/mailjump.nsf|/domcfg.nsf|/names.nsf|/homepage.nsf)"},
	{"APACHE-kylin", "body", "(url=kylin)"},
	{"C-Lodop打印服务系统", "body", "(/CLodopfuncs.js|www.c-lodop.com)"},
	{"ATLASSIAN-Confluence", "body", "(Atlassian Confluence)"},
	{"HFS", "body", "(href=\"http://www.rejetto.com/hfs/)"},
	{"Jellyfin", "body", "(content=\"http://jellyfin.org\")"},
	{"金和网络-金和OA", "body", "(/c6/Jhsoft.Web.login/PassWord.aspx|c6/Jhsoft.Web.login/PassWord.aspx)"},
	{"JumpServer", "body", "(<title>JumpServer</title>)"},
	{"金蝶云星空管理中心", "body", "(<title>金蝶云星空 管理中心</title>|金蝶云星空管理中心)"},
	{"K8S管理面板-KubePi", "body", "(<title>KubePi</title>)"},
	{"WIFISKY7层流控路由器", "body", "<title>WIFISKY 7层流控路由器</title>|深圳市领空技术有限公司|WIFISKY 7层流控路由器"},
	{"Nacos", "body", "(<title>Nacos</title>)"},
	{"Pulse Connect Secure", "body", "(/dana-na/imgs/space.gif)"},
	{"h5ai", "body", "(powered by h5ai)"},
	{"天融信脆弱性扫描与管理系统", "body", "(/js/report/horizontalReportPanel.js)"},
	{"天融信网络审计系统", "body", "(onclick=dlg_download())"},
	{"天融信日志收集与分析系统", "body", "(天融信日志收集与分析系统)"},
	{"URP教务系统", "body", "(北京清元优软科技有限公司)"},
	{"科来RAS", "body", "(科来软件 版权所有|i18ninit.min.js)"},
	{"正方OA", "body", "(zfoausername)"},
	{"希尔OA", "body", "(/heeroa/login.do)"},
	{"泛普建筑工程施工OA", "body", "(/dwr/interface/LoginService.js)"},
	{"中望OA", "body", "(/IMAGES/default/first/xtoa_logo.png|/app_qjuserinfo/qjuserinfoadd.jsp)"},
	{"海天OA", "body", "(HTVOS.js)"},
	{"信达OA", "body", "(http://www.xdoa.cn</a>)"},
	{"任我行CRM", "body", "(CRM_LASTLOGINUSERKEY)"},
	{"Spammark邮件信息安全网关", "body", "(/cgi-bin/spammark?empty=1)"},
	{"winwebmail", "body", "(WinWebMail Server|images/owin.css)"},
	{"浪潮政务系统", "body", "(LangChao.ECGAP.OutPortal|OnlineQuery/QueryList.aspx)"},
	{"网神防火墙", "body", "(css/lsec/login.css)"},
	{"帕拉迪统一安全管理和综合审计系统", "body", "(module/image/pldsec.css)"},
	{"蓝盾BDWebGuard", "body", "(BACKGROUND: url(images/loginbg.jpg) #e5f1fc)"},
	{"Huawei SMC", "body", "(Script/SmcScript.js?version=)"},
	{"coremail", "body", "(/coremail/bundle/|contextRoot: \"/coremail\"|coremail/common)"},
	{"activemq", "body", "(activemq_logo|Manage ActiveMQ broker)"},
	{"锐捷数据库安全审计系统", "body", "(锐捷数据库安全审计系统)"},
	{"锐捷网络", "body", "(static/img/title.ico|support.ruijie.com.cn|Ruijie - NBR|eg.login.loginBtn|锐捷网络股份有限公司|/pub/img5/public/no600x140.png)"},
	{"禅道", "body", "(/theme/default/images/main/zt-logo.png|zentaosid|欢迎使用禅道集成运行环境|禅道软件旗下产品)"},
	{"weblogic", "body", "(/console/framework/skins/wlsconsole/images/login_WebLogic_branding.png|Welcome to Weblogic Application Server|<i>Hypertext Transfer Protocol -- HTTP/1.1</i>|<TITLE>Error 404--Not Found</TITLE>|Welcome to Weblogic Application Server|<title>Oracle WebLogic Server 管理控制台</title>)"},
	{"致远OA", "body", "(/seeyon/USER-DATA/IMAGES/LOGIN/login.gif|/seeyon/common/)"},
	{"蓝凌EIS智慧协同平台", "body", "(/scripts/jquery.landray.common.js)"},
	{"深信服ssl-vpn", "body", "(login_psw.csp|loginPageSP/loginPrivacy.js|/por/login_psw.csp)"},
	{"泛微OA", "body", "(/spa/portal/public/index.js|wui/theme/ecology8/page/images/login/username_wev8.png|/wui/index.html#/?logintype=1)"},
	{"Swagger UI", "body", "(/swagger-ui.css|swagger-ui-bundle.js|swagger-ui-standalone-preset.js)"},
	{"金蝶政务GSiS", "body", "(/kdgs/script/kdgs.js|HTML5/content/themes/kdcss.min.css|/ClientBin/Kingdee.BOS.XPF.App.xap)"},
	{"蓝凌OA", "body", "(蓝凌软件|StylePath:\"/resource/style/default/\"|/resource/customization|sys/ui/extend/theme/default/style/icon.css|sys/ui/extend/theme/default/style/profile.css)"},
	{"用友NC", "body", "(Yonyou UAP|YONYOU NC|/Client/Uclient/UClient.dmg|logo/images/ufida_nc.png|iufo/web/css/menu.css|/System/Login/Login.asp?AppID=|/nc/servlet/nc.ui.iufo.login.Index)"},
	{"用友IUFO", "body", "(iufo/web/css/menu.css)"},
	{"TELEPORT堡垒机", "body", "(/static/plugins/blur/background-blur.js)"},
	{"JEECMS", "body", "(/r/cms/www/red/js/common.js|/r/cms/www/red/js/indexshow.js|Powered by JEECMS|JEECMS|/jeeadmin/jeecms/index.do)"},
	{"帝国网站CMS", "body", "(Powered by .*CMS)"},
	{"目录遍历", "body", "(Directory listing for /)"},
	{"向日葵", "body", "({\"success\":false,\"msg\":\"Verification failure\"})"},
	{"Kubernetes", "body", "(Kubernetes Dashboard</title>|Kubernetes Enterprise Manager|Mirantis Kubernetes Engine|Kubernetes Resource Report)"},
	{"WordPress", "body", "(/wp-login.php?action=lostpassword|WordPress</title>|WordPress站点)"},
	{"RabbitMQ", "body", "(RabbitMQ Management)"},
	{"Spring env", "body", "(logback)"},
	{"ueditor", "body", "(ueditor.all.js|UE.getEditor)"},
	{"亿邮电子邮件系统", "body", "(亿邮电子邮件系统|亿邮邮件整体解决方案)"},
	{"ZABBIX-监控系统", "cookie", "(zbx_session)"},
	{"万户OA", "cookie", "(LocLan)"},
	{"360网站卫士", "headers", "(360wzws|CWAP-waf|zhuji.360.cn|X-Safe-Firewall)"},
	{"绿盟防火墙", "headers", "(NSFocus)"},
	{"Anquanbao", "headers", "(Anquanbao)"},
	{"BaiduYunjiasu", "headers", "(yunjiasu)"},
	{"BigIP", "headers", "(BigIP|BIGipServer)"},
	{"BinarySEC", "headers", "(binarysec)"},
	{"BlockDoS", "headers", "(BlockDos.net)"},
	{"CloudFlare", "headers", "(cloudflare)"},
	{"Cloudfront", "headers", "(cloudfront)"},
	{"Comodo", "headers", "(Protected by COMODO)"},
	{"IBM-DataPower", "headers", "(X-Backside-Transport)"},
	{"DenyAll", "headers", "(sessioncookie=)"},
	{"dotDefender", "headers", "(dotDefender)"},
	{"Incapsula", "headers", "(X-CDN|Incapsula)"},
	{"Jiasule", "headers", "(jsluid=)"},
	{"KONA", "headers", "(AkamaiGHost)"},
	{"ModSecurity", "headers", "(Mod_Security|NOYB)"},
	{"NetContinuum", "headers", "(Cneonction|nnCoection|citrix_ns_id)"},
	{"Newdefend", "headers", "(newdefend)"},
	{"Safe3", "headers", "(Safe3WAF|Safe3 Web Firewall)"},
	{"Safedog", "headers", "(Safedog|WAF/2.0)"},
	{"SonicWALL", "headers", "(SonicWALL)"},
	{"Stingray", "headers", "(X-Mapping-)"},
	{"Sucuri", "headers", "(Sucuri/Cloudproxy)"},
	{"Usp-Sec", "headers", "(Secure Entry Server)"},
	{"Varnish", "headers", "(varnish)"},
	{"Wallarm", "headers", "(wallarm)"},
	{"WebKnight", "headers", "(WebKnight)"},
	{"Yundun", "headers", "(YUNDUN)"},
	{"Yunsuo", "headers", "(yunsuo)"},
	{"Coding pages", "header", "(Coding Pages)"},
	{"Shiro", "headers", "(=deleteMe|rememberMe=)"},
	{"360主机卫士", "headers", "(zhuji.360.cn)"},
	{"Nagios", "headers", "(Nagios Access)"},
	{"泛微OA", "headers", "(ecology_JSessionid)"},
	{"CISCO_VPN", "headers", "(webvpn)"},
	{"o2security_vpn", "headers", "(client_param=install_active)"},
	{"linksys-vpn", "headers", "(linksys-vpn)"},
	{"Jboss", "headers", "(JBoss)"},
	{"泛微E-mobile", "headers", "(EMobileServer)"},
	{"ThinkPHP", "headers", "(ThinkPHP)"},
	{"Laravel", "headers", "(laravel_session)"},
	{"帆软报表", "headers", "(数据决策系统)"},
	{"华夏ERP", "headers", "(华夏ERP)"},
	{"Nagios", "headers", "(nagios admin)"},
	{"weblogic", "headers", "(WebLogic)"},
	{"dubbo", "headers", "(Basic realm=\"dubbo\")"},
	{"DenyAll", "headers", "(sessioncookie=)"},
	{"Gogs简易Git服务", "cookie", "(i_like_gogs)"},
	{"Gitea简易Git服务", "cookie", "(i_like_gitea)"},
	{"Nexus", "cookie", "(NX-ANTI-CSRF-TOKEN)"},
	{"Harbor", "cookie", "(harbor-lang)"},
	{"禅道", "cookie", "(zentaosid)"},
	{"协众OA", "cookie", "(CNOAOASESSID)"},
	{"atmail-WebMail", "cookie", "(atmail6)"},
	{"phpMyAdmin", "cookie", "(pma_lang|phpMyAdmin)"},
	{"金和OA", "cookie", "(ASPSESSIONIDSSCDTDBS)"},
	{"jeesite", "cookie", "(jeesite.session.id)"},
	{"拓尔思SSO", "cookie", "(trsidsssosessionid)"},
	{"拓尔思WCMv7/6", "cookie", "(com.trs.idm.coSessionId)"},
	{"飞企互联-FE业务协作平台", "body", "(flyrise.stopBackspace.js)"},
	{"用友GRP-U8", "body", "(GRP-U8)"},
	{"网御星云-上网行为管理系统", "body", "<title>网御上网行为管理系统</title>"},
	{"Apache-APISIX", "body", "(<title>Apache APISIX Dashboard</title>)"},
	{"Alibaba-Sentinel", "body", "(<title>Sentinel Dashboard</title>)"},
	{"Tencent-企业微信", "body", "(熟悉的沟通体验，多终端登录，随时随地沟通协作|<title>企业微信</title>|让每个企业都有自己的微信)"},
	{"畅捷通-TPlus", "body", "<title>畅捷通 T"},
	{"中国移动-禹路由", "headers", "GoAhead-Webs"},
	{"SMARTBI", "headers", "CP=CAO PSA OUR"},
	{"SMARTBI", "body", "/smartbi/index.jsp"},
	{"广联达OA", "body", "/Services/Identification"},
	{"EAA益和应用接入系统", "body", "EAA益和应用接入系统"},
	{"深信服-应用交付管理系统", "body", "(这些在统一登录版本没用到 先注释掉 waf去掉白名单|/report/index.php)"},
	{"网御星云-上网行为管理系统", "body", "<title>网御上网行为管理系统</title>"},
	{"飞致云-DateEase", "body", "/css/chunk-0c21a9c5.34d69e98.css"},
	{"iStoreOS软路由", "body", "(<title>K2</title>|LuCI - Lua Configuration Interface)"},
	{"斐讯路由器", "body", "iStoreOS - 首页"},
	{"Apache-Solr-Admin", "body", "<title>Solr Admin</title>"},
	{"用友-时空KSOA", "body", "(<title>企业信息系统门户</title>|innerlogin.jsp)"},
	{"NUUO摄像头", "body", "<title>Network Video Recorder Login</title>"},
	{"人力资源信息管理系统", "body", "(<title>人力资源信息管理系统</title>|<div class=\"hj-hy-all-one-logo)"},
	{"EasyCVR视频管理平台", "body", "<title>EasyCVR</title>"},
	{"Docker-RemoteAPI", "headers", "(Api-Version|X-Docker-Registry-Version)"},
	{"通天星CMS", "body", "url=808gps/login.html"},
	{"用友-移动系统管理", "headers", "W/\"102-1379069896000\""},
	{"腾讯-TDSQL", "cookie", "tdsqlchitusessid"},
	{"腾讯-TDSQL", "body", "<title>TDSQL"},
	{"Juniper", "body", "Juniper Web Device Manager"},
	{"智能流控路由器", "server", "FSM-Webs"},
	{"Gerapy", "body", "(<title>Gerapy</title>)"},
	{"山石网科云安全审计平台", "cookie", "(SMC_SESSION)"},
	{"山石网科云安全审计平台", "cookie", "(SMCCONSOLE_SESSION)"},
	{"昂楷科技-数据库审计平台", "body", "navigator.appVersion.split"},
	{"pikachu漏洞靶场", "body", "Get the pikachu"},
	{"IIS", "body", "IIS Windows Server"},
	{"联软网络智能准入系统", "body", "Error 50x (Not Found)!!!"},
	{"Tenda路由器", "body", "<title>Tenda | LOGIN</title>"},
	{"狮子鱼CMS", "body", "/seller\\.php\\?s=/Public/login"},
	{"泛微-EOffice", "body", "(海泰key验证|泛微软件)"},
	{"CellinxNVT摄像机", "body", "(NVT-string\\.js)"},
	{"泛微云桥e-Bridge", "body", "(<title>泛微云桥e-Bridge</title>)"},
	{"Mongo-express", "body", "(<title>Home - Mongo Express</title>)"},
	{"福建科立迅通信调度平台", "body", "(app/structure/departments.php)"},
	{"HFS", "cookie", "HFS_SID_"},
	{"Serv-U", "headers", "(Serv-U)"},
	{"NexusManager", "body", "(Sonatype Nexus Repository)"},
	{"clickhouse-client", "body", "(clickhouse-client)"},
	{"clickhouse", "body", "Ok."},
	{"天问物业ERP系统", "body", "/HM/M_main/Frame/login.aspx"},
	{"天清汉马VPN", "body", `<p id="notice_content">欢迎使用VPN安全网关</p>`},
	{"D-LinkNAS", "body", `var re=/root|anonymous|nobody|administrator|ftp|guest|squeezecenter|sshd|messagebus|netdev/i;`},
	{"天问物业ERP系统", "body", "/HM/M_main/Frame/login.aspx"},
	{"神州数码DCME-320出口网关", "body", `style/blue/css/dcn_ui.css`},
	{"Crocus系统", "body", `Plugin/RegisterLogin/Default.jsp`},
	{"emlog博客", "body", `使用emlog搭建的站点`},
	{"IStoreOS软路由", "body", `- LuCI`},
	{"Pichome", "body", `(<title>登录 - pichome </title>)`},
	{"vite", "body", `/@vite/client"`},
	{"明源云-ERP系统", "icohash", "383f3ea0be963d56cb16b012885261a4"},
	{"FortiOS", "icohash", "e462005902f81094ab3de44e4381de19"},
	{"康盛创想-Discuz", "icohash", "da29fc7c73e772825df360b435174eda"},
	{"康盛创想-Discuz", "icohash", "e8535ded975539ff5d90087d0a463f3e"},
	{"通达OA", "icohash", "ed0044587917c76d08573577c8b72883"},
	{"HIKVISION-运行管理中心", "icohash", "e05b47d5ce11d2f4182a964255870b76"},
	{"明源云-ERP系统", "icohash", "b09e6a22f7fcac7bc1019ca15640f8e6"},
	{"EasyCVR视频管理平台", "icohash", "04c37abf9f61c6f7301b21fd34c5fd9e"},
	{"Hoverfly", "icohash", "baa090fbc1418c8c4971002cc5459574"},
	{"同为数码-DVR", "icohash", "3aef8b29c4866f96a539730fab53a88f"},
	{"Palo Alto-Expedition Migration tool", "icohash", "fbea6617fdbe887f37642f133405885b"},
	{"奇安信网神-SecSSL VPN", "icohash", "4cf31d7e53197f79b435b66cc8cb3098"},
	{"麦克奥迪-数字切片管理系统", "icohash", "4d8f8a2638d12502bae80bf3f13f25b8"},
	{"维盟-智能路由管理系统", "icohash", "715c49c5512d763084a4082c27d935e1"},
	{"恒生电子-JRES开发平台", "icohash", "d5542d613c425839622d8c049dd34136"},
	{"友点-企业网站管理系统", "icohash", "40d924d96b8903a41252bc0a8eb3f39b"},
	{"Juniper", "icohash", "f1876a80546b3986dbb79bad727b0374"},
	{"锐明-Crocus系统", "icohash", "9d135ee1babb6f7ed2ffc2199aac2713"},
	{"锐明-Crocus系统", "body", "<title>Crocus</title>|Welcome to Crocus|Crocus 欢迎使用|<title>Crocus系统欢迎你</title>"},
	{"同鑫-人力资源管理系统", "icohash", "3ded7f63a951535b0ae501a7fac6b76d"},
	{"同鑫-人力资源管理系统", "body", "/TX.CDN/js/orgchart.js"},
	{"停车场后台管理系统", "icohash", "b3f2247c8946c4d851d0fa97fc5f0ddf"},
	{"灵当科技-CRM", "icohash", "40464533fc5c0a8f4fc845d280e09823"},
	{"中科智远-综合监管云平台", "icohash", "69688396e15d4677ca5ae0aa6517a54c"},
	{"网龙科技-龙腾CMS", "icohash", "ad276ab52226af630ab00d6cbb7e5a3f"},
	{"Journyx", "icohash", "9b744462675c530d8252866b6c6ae8e0"},
	{"驰骋软件-BPM", "icohash", "e94d8e4557d8f7097563c97f302eeedd"},
	{"环境自动监测监控系统", "icohash", "3f841b4b7465e888163f4f7573d3ec3e"},
	{"天智云-智造管理平台", "icohash", "6f556f71fca0f850844bae56bb1abd43"},
	{"爱米云-悦库企业网盘", "icohash", "79c06d04db5a15316729fe0e5ae4d0b6"},
	{"汇智软件-ERP", "icohash", "4b808d57f14201f928f9c35572ab94a4"},
	{"Kafka-Map", "icohash", "c92b85a5b907c70211f4ec25e29a8c4a"},
	{"Kafka-UI", "body", "UI for Apache Kafka"},
	{"Docassemble", "icohash", "d909bfe75c654accec4ab8aa6b585613"},
	{"美特软件-CRM", "icohash", "e091e729af5a1f477ba33baa01e773b4"},
	{"合勤科技-NAS", "icohash", "6a7e6701a2ef451032f73927ea20aa45"},
	{"魔方恒久-网表ERP", "icohash", "7cdc7033cbe79069de1d5d096a6d721c"},
	{"Serv-U", "icohash", "2815c2c03732de3af5d4387f87efeb81"},
	{"汉得-SRM云平台", "icohash", "1700249a2f92a04de27e2a532a3f3b90"},
	{"捷顺科技-智能终端操作平台", "icohash", "2f809c4759399cae458a29f24490a114"},
	{"联软科技-数据交换系统", "icohash", "928831999d8de4c41d271319631ab01b"},
	{"Progress-Kemp LoadMaster", "icohash", "12649f4e0c5a37d4a41cbca768c8e7e0"},
	{"AJ-Report", "icohash", "6e678a5e756275becc7b6ddca181f354"},
	{"CrushFTP-FTP Server", "icohash", "297a81069094d00a052733d3a0537d18"},
	{"MajorDoMo", "icohash", "08d30f79c76f124754ac6f7789ca3ab1"},
	{"OpenAI-ChatGPT个人专用版", "icohash", "818f7e5b5f05f3fbd1782fd54e87b4f2"},
	{"OWASP-DVWA漏洞演练环境", "icohash", "69c728902a3f1df75cf9eac73bd55556"},
	{"ChatGPT Next Web", "icohash", "d3d998f48e7d3bb97d4292e19e3fadcc"},
	{"ChatGPT Next Web", "icohash", "3cb541d6e3e1233ad096da9d0c950765"},
	{"迅饶-X2Modbus网关", "icohash", "55d4ffcae305c64f00a8828698f0ca8d"},
	{"Microchip-Manager", "icohash", "4f18a9f9339547370c72d2a4503ecca4"},
	{"开源-FreeRDP", "icohash", "ee67c470e78bec947cb683e0c51a9223"},
	{"宝塔", "icohash", "9637ebd168435de51fea8193d2d89e39"},
	{"智邦国际企业管理软件", "icohash", "0ab4ed9764a33fd85da03b00f44393e1"},
	{"智邦国际企业管理软件", "body", "SYSA/images/zbintelsng.png"},
	{"润申科技-企业标准化信息管理系统", "icohash", "ae32732a35163f4a4d90f40a1f14645e"},
	{"科荣股份-AIO管理系统", "icohash", "494d1a9cf585c0b6b45462a224fccd90"},
	{"瑞友天翼-应用虚拟化系统", "icohash", "e33616cbfcf914d35164e687dc14dd01"},
	{"HIKVISION-IP网络对讲广播系统", "icohash", "e854b2eaa9e4685a95d8052d5e3165bc"},
	{"Ray", "icohash", "b01715fdffe506f5a4dc79170429446c"},
	{"开源-FUXA", "icohash", "f41c8c98d93bc3b707937d0c0cbfad04"},
	{"SysAid-Help Desk", "icohash", "5f30870725d650d7377a134c74f41cfd"},
	{"influxdata-InfluxDB", "icohash", "6429a96b2434f9bcd565f36ccbeee420"},
	{"influxdata-InfluxDB", "icohash", "c08c8f6b8e8d975b3a3c86f82aa33ec4"},
	{"Apache-神禹ShenYu", "icohash", "7e2998cf0ab2c9a0f266f90efcce764a"},
	{"Apache-神禹ShenYu", "icohash", "64904850a8e753f732981c5d0547a596"},
	{"锐捷网络-NBR路由器", "icohash", "d8d7c9138e93d43579ebf2e384745ba8"},
	{"Tenda路由器", "icohash", "fa31b29eab2da688b11d8fafc5fc6b27"},
	{"帮管客CRM客户管理系统", "icohash", "1b3a92d726db93f8327c432960cf531c"},
	{"StarRocks", "icohash", "ab1d4a39aecae972f82d356c46d393d4"},
	{"Smanga", "icohash", "f528a54b57a6dd7625df9f9d51b6613a"},
	{"若依管理系统", "icohash", "e49fd30ea870c7a820464ca56a113e6e"},
	{"若依管理系统", "icohash", "4eeb8a8eb30b70af511dcc28c11a3216"},
	{"深信服-应用交付管理系统", "icohash", "98128e625a3b98546a206c2170fb5eb3"},
	{"华为-Auth-Http-Server", "icohash", "be2408ce4716a5cfc50dd0ada76619d6"},
	{"开源防火墙pfSense", "icohash", "5567e9ce23e5549e0fcd7195f38828"},
	{"HIKVISION-运行管理中心", "icohash", "e05b47d5ce11d2f4182a964255870b76"},
	{"卓软在线", "icohash", "1fa66ff1d1d1ce0a8ba05838c1b58a15"},
	{"金蝶云星空", "icohash", "825af13371930eeb2f85cf075fa25b68"},
	{"开源-Microweber", "icohash", "15e46ec02c9509c072aae9baa1270177"},
	{"飞企互联-企业运营管理平台", "icohash", "e90223165de1b1c7ae95336f10c3fe5d"},
	{"Sophos-UTM-应用防火墙", "icohash", "41776fd18cb6fdadf3c2262a81ac0242"},
	{"锐捷网络-EasyGate", "icohash", "d8d7c9138e93d43579ebf2e384745ba8"},
	{"JetBrains-TeamCity", "icohash", "cee18e28257988b40028043e65a6c2a3"},
	{"深信服-EDR", "icohash", "0b24d4d5c7d300d50ee1cd96059a9e85"},
	{"Jorani", "icohash", "96c540e05efe5c9e11f15dd5ce70bb0f"},
	{"开源-CasaOS", "icohash", "a060abec338c194680d4d7c085b9f8f1"},
	{"飞致云-DataEase", "icohash", "38ab75cdbd557d1bcf3a29e05a91cd73"},
	{"小米路由器", "icohash", "adb23a782f28a19a731b327f1f0c4e4b"},
	{"网络监控软件PRTG", "icohash", "36b3ef286fa4befbb797a0966b456479"},
	{"Oracle-Database", "icohash", "bf24e73c0424dfab1d1517c0602db950"},
	{"飞企互联-FE业务协作平台", "icohash", "e90223165de1b1c7ae95336f10c3fe5d"},
	{"安恒-明御运维审计与风险控制系统", "icohash", "68c0a8e5a44ae856fd1a52cea1b45634"},
	{"开源-ShowDoc", "icohash", "1fbc02dc6f980f075779049bf687128a"},
	{"开源-ShowDoc", "icohash", "7ef671174190ef1ecd4494235d019606"},
	{"契约锁电子签章系统", "icohash", "ad17b1fc5025ca47f743f7e69c956b26"},
	{"宿迁鑫潮-PHPYUN-人才招聘系统", "icohash", "4acd3906ef4335eaceaa3bf2c2ee8ba2"},
	{"开源-Squidex", "icohash", "cde4738334d88711e842f4f8ebcfe5a5"},
	{"开源-Everything", "icohash", "43516830fce78bccd6a582984eaf12dd"},
	{"SPIP", "icohash", "cd9cb7a544d5748aa2e52fbae92d2083"},
	{"华为-HG路由器", "icohash", "3bbf6287532aaa501207649a630ea47d"},
	{"维盟-AC集中管理系统", "icohash", "715c49c5512d763084a4082c27d935e1"},
	{"HIKVISION-网络摄像头", "icohash", "89b932fcc47cf4ca3faadb0cfdef89cf"},
	{"HIKVISION-网络摄像头", "icohash", "efca7d9aeaad122133c65cdf628ac574"},
	{"SonicWall-SSL VPN", "icohash", "60fa7ed2309d77de1f9dc5e7c741ac48"},
	{"SonicWall-SSL VPN", "icohash", "50b0775a8151f4ec4e04fab55a619847"},
	{"绿盟防火墙", "icohash", "a91fbd46e52b1c1f8778906024bc9e15"},
	{"建文软件-工程项目管理软件", "icohash", "5837f28fce16e49bd48c5be7723484a0"},
	{"华天动力协同OA", "icohash", "b7093d421dbebf3fdd76545d4457673a"},
	{"华天动力协同OA", "body", "/htoa/js/login.js|/OAapp/htpages/app"},
	{"科友科技-堡垒机", "icohash", "438829ba6a6de920d8bca635f4807ff2"},
	{"泛微E-Mobile", "icohash", "41eca7a9245394106a09b2534d8030df"},
	{"泛微E-Mobile", "icohash", "f51746305f07a64eafa401adab364ad9"},
	{"奇安信天擎", "icohash", "e4e25f45b2a8988cb203743bd64204fe"},
	{"开源-stcms", "icohash", "27504fef88a51f77c84a8486327cfd7d"},
	{"网康-下一代防火墙", "icohash", "9252bd07dbb6277f7ae898da87dada3a"},
	{"Fortinet-SSLVPN", "icohash", "e462005902f81094ab3de44e4381de19"},
	{"Prometheus", "icohash", "5ee43b38986a144d6b5022ea8c8f748f"},
	{"Node-RED", "icohash", "818DD6AFD0D0F9433B21774F89665EEA"},
	{"Citrix-ADC应用交付控制器", "icohash", "c4106317cf0d9077ac553612502a10b0"},
	{"Citrix-ADC应用交付控制器", "icohash", "871eb7d317524611af4f05c6ba878df8"},
	{"久其财务报表", "icohash", "d0bb4db809acec6e1424889d1e9b1bec"},
	{"ZABBIX-监控系统", "icohash", "0fbe700fd7d07ec8d30ef8b3ac261484"},
	{"Apache-Skywalking", "icohash", "56d39b7b26bb71a231b7737764d1f4cc"},
	{"Apache-Skywalking", "body", "SkyWalking"},
	{"ServiceNow-云计算平台", "icohash", "4bcf9df7480dea322b80e7d5b672736a"},
	{"任我行CRM", "icohash", "8916bc9019c76afe7b9dec97ad60977e"},
	{"开源-inoERP企业管理系统", "icohash", "7ac0aaf1b8623992abf8664c4bd1b4fe"},
	{"SpiderFlow", "icohash", "7d442c5064ea0c54f949fac57e5610c4"},
	{"Esri-ArcGIS", "icohash", "4083271d68aefe9f0e606720d0fe8b32"},
	{"Apache-Storm", "icohash", "ce4f5f7ef1a664c39708fdbb821edc67"},
	{"泛微-EOffice", "icohash", "41eca7a9245394106a09b2534d8030df"},
	{"会捷通云视讯平台", "icohash", "0de87a88711a9446a9f09c53a01264c6"},
	{"悟空CRM", "icohash", "15ca17f2d0194fa685a393f4c7b0a121"},
	{"悟空CRM", "icohash", "1f73fe4c96751389f7c92b39799e2f83"},
	{"悟空CRM", "icohash", "df1ee68bf3631f46c2f4e98e2a2bf64a"},
	{"孚盟云CRM系统", "icohash", "d798209beb36c2d8efd96e58e421d060"},
	{"Mongo-Express", "icohash", "6f6256748d679d8684123363bd50a8dd"},
	{"Dapr Dashboard", "icohash", "b5653daaa64dad50b31d67f685bc2b13"},
	{"协众软件-OA", "icohash", "b684337bfa0561ffbbd99b0d992b9a45"},
	{"华测监测预警系统", "icohash", "be8e2d43a4e9bc5ce43303ce55609199"},
	{"开源-极致CMS", "icohash", "c7bfdd8a6b762a5220f7217552592115"},
	{"KYOCERA-Printer", "icohash", "457d76be8460d899c09becbc09b4a9e8"},
	{"万户OA-ezOFFICE", "icohash", "fa1f01d0392d60663d8ce936fe06096a"},
	{"启明星辰-泰合统一安全管控与审计系统", "icohash", "594fa077687dedb62e247b7961801ba5"},
	{"JGraph-Drawio", "icohash", "fab2d88b37c72d83607527573de45281"},
	{"JGraph-Drawio", "body", "<title>Flowchart Maker.*?Online Diagram Software</title>|devhost.jgraph.com/drawio/src/main"},
	{"安恒-web应用防火墙", "icohash", "498696ae273c2529c4f21b8505c4571c"},
	{"安恒-web应用防火墙", "icohash", "7cdad37e166213ca1a8fc504239db933"},
	{"D-Link-DIR 815", "icohash", "107579220745d3b21461c23024d6c4a3"},
	{"StarDot-网络摄像头", "icohash", "416ccc315c6ebb5b116a6a94471d48d0"},
	{"StarDot-网络摄像头", "icohash", "296a69b029b20939c9b8da056a43c324"},
	{"SonarQube-代码管理", "icohash", "b4e4785d5852c563b9ae47cbb7af06fe"},
	{"网康-应用安全网关（NS-ASG）", "icohash", "6b3ce2ff80f015f6c8fb90e4d0b91f8b"},
	{"Contec-SolarView Compact", "icohash", "6682621a501087b6fb934c4c01cab704"},
	{"逐浪软件-zoomla", "icohash", "d36c4978af05ab95f3579762e08abdfe"},
	{"D-Link-DIR", "icohash", "107579220745d3b21461c23024d6c4a3"},
	{"SAP-NetWeaver", "icohash", "b2bd2546ba77fec1f47425679e05f212"},
	{"Apereo-Central", "icohash", "35caf5d7084b2ed8264526d02bc2e1d3"},
	{"开源-Webmin", "icohash", "9a2006c267de04e262669d821b57ead1"},
	{"开源-Openfire", "icohash", "e4888ee8491b4eb75501996e41af6460"},
	{"Jenkins", "icohash", "23e8c7bd78e8cd826c5a6073b15068b1"},
	{"用友畅捷通-CRM", "icohash", "223dcd70f982801e9177078a818a9b59"},
	{"泛微E-Cology", "icohash", "41eca7a9245394106a09b2534d8030df"},
	{"H3C-SecPath运维审计系统", "icohash", "06db904d19c376032c22430245fdd15c"},
	{"H3C-SecPath运维审计系统", "icohash", "68c0a8e5a44ae856fd1a52cea1b45634"},
	{"H3C-SecPath运维审计系统", "icohash", "5eaae1deba6e168056026a65eb90edf1"},
	{"开源-FeiFeiCMS", "icohash", "72750e5edca884f6a44709df8756161b"},
	{"易通科技-CmsEasy", "icohash", "1d3b0614059f6a05c7c382e5a0646237"},
	{"用友-FE协作办公系统", "icohash", "e90223165de1b1c7ae95336f10c3fe5d"},
	{"用友GRP-U8", "icohash", "b41be1ccc6f9f2894e0cfcf23acf5fc0"},
	{"开源-GIBBON", "icohash", "3e4e1cc35bec1075f81f177a81bbd8dc"},
	{"开源-free5GC", "icohash", "c92b85a5b907c70211f4ec25e29a8c4a"},
	{"Discuz）", "icohash", "c028c4822428e83a358c60a93ef65381"},
	{"Linkedin-Azkaban", "icohash", "f4f03949540d643b80f247510214da9c"},
	{"开源-rpcms", "icohash", "58943b389fd0e83bb69374ef5e91c6c4"},
	{"KongA控制管理平台", "icohash", "f331e58a51be69b2266b7a2ccbf058c6"},
	{"华为-DG路由器", "icohash", "556cf2a26dae4d51318e1d73a20908e5"},
	{"紫光软件-档案管理系统", "icohash", "3e2ee25dc73811fe2a697deefe447017"},
	{"同为数码-NVMS-1000", "icohash", "3aef8b29c4866f96a539730fab53a88f"},
	{"网康-互联网控制网关", "icohash", "9252bd07dbb6277f7ae898da87dada3a"},
	{"开源-DWSurvey 问卷调查系统", "icohash", "6e8b53be9fff108c8dd70222a2bfe071"},
	{"斗象-ARL资产安全灯塔", "icohash", "fd89b71df11755eacdf82d06074ed07b"},
	{"开源-dst-admin", "icohash", "7d33071e39dc6fad65a9a718ee14ccc8"},
	{"开源-Radmind", "icohash", "321d9eecccf17be353214f2f85d74491"},
	{"北大方正-翔宇CMS系统", "icohash", "af92dd417977df95f68f241aee273482"},
	{"开源-OpenTSDB", "icohash", "7f4338a0648aaefcdb73240e8420387b"},
	{"开源-RockMongo", "icohash", "58f12fe033e8baa53bd56c6759687db2"},
	{"联软科技-UniAccess终端安全管理系统", "icohash", "da9ce98e9a52ccb605d112c0437bcbc1"},
	{"RustDesk远程工具", "title", `.*RustDesk.*`},
	{"火绒", "cert", "huorong.cn"},
	{"深信服", "cert", "SANGFOR"},
	{"xui", "body", `basePath\s*\+\s*'xui/'`},
	{"ikuai产品(无线网络)", "cert", "ikuai8.com"},
	{"GeoServer", "body", `(?i)geoserver`},
	{"xui", "body", `location.href = basePath + 'xui/'`},
	{"九思OA", "body", "location.href.*?/jsoa/login.jsp"},
}
