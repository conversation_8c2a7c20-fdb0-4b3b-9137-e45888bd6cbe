<!DOCTYPE html>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Golin Web</title>
    <style>
        body {
            font-family: Arial, "微软雅黑", sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(120deg, #e06645 0%, #007bff 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            font-size: 3rem;
            color: #333;
            margin-bottom: 50px;
        }

        .btn-group {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 1.2rem;
            text-align: center;
            text-decoration: none;
            background-color: #4c5fe0;
            color: rgb(252, 252, 252);
            transition: background-color 0.3s, box-shadow 0.3s, transform 0.2s ease-out;
            box-shadow: 0 4px 14px 0 rgba(65, 135, 214, 0.39);
        }

        .btn:hover {
            background-color: #18e27d;
            box-shadow: 0 6px 20px rgba(100, 255, 180, 0.5);
            transform: scale(1.4);
        }

        .footer {
            text-align: center;
            margin-top: 50px;
        }

        .footer a {
            text-decoration: none;
            color: #2dcd57;
        }

        .footer a:hover {
            text-decoration: underline;
        }
    </style>
	    <script>
        function openPopupWindow(url) {
            const dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : screen.left;
            const dualScreenTop = window.screenTop != undefined ? window.screenTop : screen.top;

            const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
            const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

            const systemZoom = width / window.screen.availWidth;
            const left = (width - 960) / 2 / systemZoom + dualScreenLeft;
            const top = (height - 600) / 2 / systemZoom + dualScreenTop;

            const newWindow = window.open(url, '_blank', 'scrollbars=yes, resizable=yes, toolbar=no, location=no, status=no, menubar=no, width=960, height=600, top=' + top + ', left=' + left);

            if (window.focus) {
                newWindow.focus();
            }
        }
    </script>
</head>

<body>
    <div class="container">
        <h1>Golin 网络安全等级保护核查工具</h1>
        <div class="btn-group">
            <a href="/golin/indexfile" class="btn"  target="_blank" >多主机核查</a>
            <a href="/golin/index"  class="btn"  target="_blank">单主机核查</a>
 			<a href="/golin/history" target="_blank" class="btn">历史记录</a>
            <a href="/golin/dj"  class="btn"  target="_blank">模拟定级</a>
            <a href="https://www.yuque.com/gaoys/dbcp/help"  class="btn"  target="_blank">等保常问</a>
            <a href="/golin/update" class="btn" class="btn">检查更新</a>
            <a href="https://picdm.sunbangyan.cn/2023/10/09/r74hp1.png" class="btn" target="_blank">联系作者</a>
        </div>
        <div class="footer">
            <p>version:{{ .Version }} 如觉得对自己有帮助点个星星吧~ <a href="https://github.com/selinuxG/Golin-cli" target="_blank">Github</a></p>
        </div>
    </div>
</body>

</html>
